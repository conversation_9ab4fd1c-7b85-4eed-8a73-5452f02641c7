// ================== 验证链接提取模块 ==================
class VerificationExtractor {
    constructor() {
        this.debugMode = true;
    }

    // 从页面内容中提取验证链接
    extractVerificationLinks() {
        const links = [];
        
        try {
            // 获取页面的所有文本内容
            const pageText = document.body.innerText || document.body.textContent || '';
            
            // 尝试解析JSON响应（如果页面显示的是JSON）
            try {
                const jsonMatch = pageText.match(/\{.*\}/);
                if (jsonMatch) {
                    const jsonData = JSON.parse(jsonMatch[0]);
                    if (jsonData.data) {
                        // 从JSON数据中提取链接
                        const dataLinks = this._extractLinksFromText(jsonData.data);
                        links.push(...dataLinks);
                    }
                }
            } catch (e) {
                // 如果不是JSON，继续其他方式提取
            }

            // 从整个页面文本中提取链接
            const textLinks = this._extractLinksFromText(pageText);
            links.push(...textLinks);

            // 从HTML中直接查找链接
            const htmlLinks = this._extractLinksFromHTML();
            links.push(...htmlLinks);

            // 去重并过滤验证链接
            const uniqueLinks = [...new Set(links)];
            const verificationLinks = uniqueLinks.filter(link => this._isVerificationLink(link));

            this._safeLog('Found verification links:', verificationLinks);
            return verificationLinks;

        } catch (error) {
            this._safeLog('Error extracting links:', error);
            return [];
        }
    }

    // 从文本中提取链接
    _extractLinksFromText(text) {
        const links = [];
        
        // 匹配各种可能的验证链接格式
        const linkPatterns = [
            // 标准HTTP/HTTPS链接
            /https?:\/\/[^\s<>"']+/gi,
            // 可能被编码的链接
            /(?:https?%3A%2F%2F|https?:\/\/)[^\s<>"']+/gi
        ];

        linkPatterns.forEach(pattern => {
            const matches = text.match(pattern);
            if (matches) {
                matches.forEach(match => {
                    // 解码URL编码的链接
                    try {
                        const decodedLink = decodeURIComponent(match);
                        links.push(decodedLink);
                    } catch (e) {
                        links.push(match);
                    }
                });
            }
        });

        return links;
    }

    // 从HTML元素中提取链接
    _extractLinksFromHTML() {
        const links = [];
        
        // 查找所有a标签
        const aElements = document.querySelectorAll('a[href]');
        aElements.forEach(a => {
            if (a.href) {
                links.push(a.href);
            }
        });

        // 查找可能包含链接的其他元素
        const textElements = document.querySelectorAll('*');
        textElements.forEach(element => {
            if (element.children.length === 0) { // 只检查叶子节点
                const text = element.textContent || element.innerText || '';
                const textLinks = this._extractLinksFromText(text);
                links.push(...textLinks);
            }
        });

        return links;
    }

    // 判断是否为验证链接
    _isVerificationLink(link) {
        if (!link || typeof link !== 'string') return false;
        
        // 验证链接的特征
        const verificationPatterns = [
            /confirm/i,
            /verify/i,
            /activation/i,
            /validate/i,
            /lpts\.me/i,
            /\/a\/[a-zA-Z0-9]+/,
            /token=/i,
            /code=/i
        ];

        return verificationPatterns.some(pattern => pattern.test(link));
    }

    _safeLog(...args) {
        if (this.debugMode) {
            console.log('[Verification Extractor]', ...args);
        }
    }
}

// ================== 悬浮窗模块 ==================
class FloatingWindow {
    constructor() {
        this.element = null;
        this.isDragging = false;
        this.currentX = 0;
        this.currentY = 0;
        this.initialX = 0;
        this.initialY = 0;
        this.xOffset = 0;
        this.yOffset = 0;
    }

    create() {
        // 创建悬浮窗容器
        this.element = document.createElement('div');
        this.element.id = 'verification-extractor-float';
        this.element.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            background: #2196F3;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: move;
            user-select: none;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            transition: background-color 0.3s;
            min-width: 120px;
            text-align: center;
        `;

        // 创建按钮
        const button = document.createElement('button');
        button.textContent = '提取验证链接';
        button.style.cssText = `
            background: none;
            border: none;
            color: white;
            font-size: 14px;
            cursor: pointer;
            padding: 0;
            margin: 0;
            font-weight: bold;
            width: 100%;
        `;

        this.element.appendChild(button);
        document.body.appendChild(this.element);

        // 添加拖拽功能
        this._setupDragListeners();

        return button;
    }

    updateStatus(text, color = '#2196F3') {
        if (this.element) {
            this.element.style.background = color;
            const button = this.element.querySelector('button');
            if (button) {
                button.textContent = text;
            }
        }
    }

    _setupDragListeners() {
        this.element.addEventListener('mousedown', (e) => {
            if (e.target === this.element) {
                this.isDragging = true;
                this.initialX = e.clientX - this.xOffset;
                this.initialY = e.clientY - this.yOffset;
                this.element.style.cursor = 'grabbing';
            }
        });

        document.addEventListener('mousemove', (e) => {
            if (this.isDragging) {
                e.preventDefault();
                this.currentX = e.clientX - this.initialX;
                this.currentY = e.clientY - this.initialY;
                this.xOffset = this.currentX;
                this.yOffset = this.currentY;
                this.element.style.transform = 
                    `translate(${this.currentX}px, ${this.currentY}px)`;
            }
        });

        document.addEventListener('mouseup', () => {
            if (this.isDragging) {
                this.isDragging = false;
                this.element.style.cursor = 'move';
            }
        });
    }
}

// ================== 主执行逻辑 ==================
const isApiPage = () => {
    return window.location.href.includes('api.bujidian.com/getMailInfo');
};

const init = async () => {
    if (!isApiPage()) return;

    console.log('Verification extractor loaded on API page');

    // 创建悬浮窗
    const floatingWindow = new FloatingWindow();
    const triggerButton = floatingWindow.create();

    // 创建验证提取器
    const extractor = new VerificationExtractor();

    // 点击按钮时提取验证链接
    triggerButton.addEventListener('click', async () => {
        triggerButton.disabled = true;
        floatingWindow.updateStatus('提取中...', '#FF9800');
        
        try {
            // 等待一小段时间确保页面完全加载
            await new Promise(r => setTimeout(r, 500));
            
            const verificationLinks = extractor.extractVerificationLinks();
            
            if (verificationLinks.length > 0) {
                // 找到验证链接，打开第一个
                const link = verificationLinks[0];
                window.open(link, '_blank');
                
                floatingWindow.updateStatus('已打开验证链接', '#4CAF50');
                console.log('Opened verification link:', link);
                
                // 如果有多个链接，在控制台显示所有链接
                if (verificationLinks.length > 1) {
                    console.log('Additional verification links found:', verificationLinks.slice(1));
                }
            } else {
                floatingWindow.updateStatus('未找到验证链接', '#F44336');
                console.log('No verification links found');
            }
            
            // 3秒后恢复按钮状态
            setTimeout(() => {
                triggerButton.disabled = false;
                floatingWindow.updateStatus('提取验证链接', '#2196F3');
            }, 3000);
            
        } catch (error) {
            console.error('Error during extraction:', error);
            floatingWindow.updateStatus('提取失败', '#F44336');
            
            setTimeout(() => {
                triggerButton.disabled = false;
                floatingWindow.updateStatus('提取验证链接', '#2196F3');
            }, 3000);
        }
    });
};

// 页面加载完成后初始化
if (document.readyState === 'complete') {
    init();
} else {
    window.addEventListener('load', init);
}

// 也监听DOM变化，以防页面是动态加载的
if (document.readyState !== 'complete') {
    document.addEventListener('DOMContentLoaded', init);
}
