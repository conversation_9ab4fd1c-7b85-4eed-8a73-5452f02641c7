(function() {
    'use strict';

    // ================== 防检测核心模块 ==================
    class AntiDetectionSystem {
        constructor() {
            // 移除不需要的状态
        }

        // 简化延迟，返回最小值
        delay(type) {
            return 0;
        }

        // 简化鼠标移动，直接触发事件
        async moveMouse(target) {
            const rect = target.getBoundingClientRect();
            document.dispatchEvent(new MouseEvent('mousemove', {
                clientX: rect.left + rect.width/2,
                clientY: rect.top + rect.height/2,
                bubbles: true
            }));
        }

        // 简化点击，直接触发事件
        async performClick(element) {
            element.dispatchEvent(new MouseEvent('click', {
                bubbles: true
            }));
        }
    }

    // ================== 注册功能模块 ==================
    class RegistrationFlow {
        constructor() {
            this.antiDetect = new AntiDetectionSystem();
            this.debugMode = false;
        }

        async execute() {
            try {
                const data = await this._getRegistrationData();
                await this._fillForm(data);
                await this._runGlobalValidation(); // 新增最终提交检查

                // 注册完成后打开API页面
                await this._openApiPage();
            } catch (error) {
                this._safeLog('Registration error:', error);
            }
        }

        async _getRegistrationData() {
            if (typeof chrome !== 'undefined' && chrome.storage?.local) {
                return new Promise(resolve => {
                    chrome.storage.local.get(['registrationData'], result => {
                        resolve(result.registrationData || this._getFallbackData());
                    });
                });
            }
            return this._getFallbackData();
        }

        _getFallbackData() {
            return {
                randomName: 'John Doe',
                birthdayWithGender: '1990-01-01 男',
                email: '<EMAIL>',
                password: 'Test123!'
            };
        }

        async _fillForm(data) {
            await this._simulateHumanBehavior();
            await this._processFormSteps(data);
        }

        async _simulateHumanBehavior() {
            // 移除人类行为模拟
        }

        async _processFormSteps(data) {
            const steps = [
                async () => {
                    await this._fillEmail(data.email);
                    await new Promise(r => setTimeout(r, 1000)); // 第一步提交后等待1秒
                },
                async () => {
                    await this._fillPersonalInfo(data);
                    await new Promise(r => setTimeout(r, 1000)); // 第二步提交后等待1秒
                },
                async () => {
                    await this._setPassword(data.password);
                    await new Promise(r => setTimeout(r, 1000)); // 第三步提交后等待1秒
                }
            ];

            for (const step of steps) {
                await step();
            }
        }

        async _fillEmail(email) {
            const input = document.querySelector('#username');
            if (!input) return;

            await this.antiDetect.moveMouse(input);
            await this.antiDetect.performClick(input);
            await this._typeWithHumanRhythm(input, email);
            await this._submitForm();
        }

        async _fillPersonalInfo(data) {
            const [firstName, lastName] = data.randomName.split(' ');
            const [birthDate] = data.birthdayWithGender.split(' ');
            const [year, month, day] = birthDate.split('-');

            await this._fillName(firstName, lastName);
            await this._fillBirthDate(year, month, day);
            await this._submitForm();
        }

        async _fillName(firstName, lastName) {
            const firstNameInput = document.querySelector('input[name="firstName"]');
            const lastNameInput = document.querySelector('input[name="lastName"]');

            await this._typeWithHumanRhythm(firstNameInput, firstName);
            await this._typeWithHumanRhythm(lastNameInput, lastName);
        }

        async _fillBirthDate(year, month, day) {
            const inputs = {
                year: document.querySelector('input[name="year"]'),
                month: document.querySelector('input[name="month"]'),
                day: document.querySelector('input[name="day"]')
            };

            for (const [type, element] of Object.entries(inputs)) {
                if (!element) continue;
                await this.antiDetect.performClick(element);
                await this._typeWithHumanRhythm(element, { year, month, day }[type]);
            }
        }

        async _setPassword(password) {
            const passwordInput = document.querySelector('input[name="password"]');
            
            if (passwordInput) {
                passwordInput.focus();
                await new Promise(r => setTimeout(r, 100));
                
                passwordInput.value = password;
                passwordInput.dispatchEvent(new Event('focus', { bubbles: true }));
                passwordInput.dispatchEvent(new Event('input', { bubbles: true }));
                passwordInput.dispatchEvent(new Event('change', { bubbles: true }));
                passwordInput.dispatchEvent(new Event('blur', { bubbles: true }));
                
                await this._updateValidationState(passwordInput);
                await new Promise(r => setTimeout(r, 300));
            }

            const privacyCheckbox = document.querySelector('input[name="privacyPolicy"], input[id=":r0:"], input[required][type="checkbox"]');
            
            if (privacyCheckbox && !privacyCheckbox.checked) {
                privacyCheckbox.checked = true;
                privacyCheckbox.setAttribute('checked', 'checked');
                privacyCheckbox.dispatchEvent(new Event('change', { bubbles: true }));
                privacyCheckbox.dispatchEvent(new MouseEvent('click', { 
                    bubbles: true,
                    cancelable: true,
                    view: window
                }));
                await new Promise(r => setTimeout(r, 300));
            }

            // 设置空的NeuroID数据
            const neuroInput = document.querySelector('input[name="nds-pmd"]');
            if (neuroInput) {
                neuroInput.value = "";
            }

            // 查找并点击提交按钮
            const submitSelectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button.submit-btn',
                'button.next-btn',
                'button.el-button--primary',
                'button.ant-btn-primary',
                '.submit-button',
                '.next-button'
            ];

            for (const selector of submitSelectors) {
                const buttons = document.querySelectorAll(selector);
                for (const button of buttons) {
                    if (button && 
                        !button.disabled && 
                        (button.offsetWidth > 0 || button.offsetHeight > 0) && 
                        window.getComputedStyle(button).display !== 'none') {
                        button.click();
                        await new Promise(r => setTimeout(r, 1000));
                        return;
                    }
                }
            }
        }

        async _runGlobalValidation() {
            // 触发表单的提交事件
            const form = document.querySelector('form');
            if (form) {
                form.dispatchEvent(new Event('submit', {
                    bubbles: true,
                    cancelable: true
                }));

                // 特殊处理Angular表单
                if ('ng' in window) {
                    const $form = angular.element(form);
                    const $ctrl = $form.controller('form');
                    if ($ctrl) {
                        $ctrl.$setSubmitted();
                        $ctrl.$validate();
                    }
                }
            }

            // 等待异步验证（最多3秒）
            const start = Date.now();
            while (Date.now() - start < 3000) {
                if (!document.querySelector('.loading-indicator')) break;
                await new Promise(r => setTimeout(r, 100));
            }
        }

        async _typeWithHumanRhythm(element, text) {
            element.value = text;
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('change', { bubbles: true }));
            await this._updateValidationState(element);
        }

        async _updateValidationState(element) {
            // 添加验证类（根据常见框架模式）
            const addValidationClass = () => {
                element.classList.remove('invalid');
                element.classList.add('valid', 'dirty', 'touched');
                element.setAttribute('data-valid', 'true');
            };

            // 分阶段触发
            await new Promise(r => requestAnimationFrame(r));
            addValidationClass();
            await new Promise(r => setTimeout(r, 50));
            addValidationClass();
        }

        async _submitForm() {
            const submitButton = document.querySelector('button[type="submit"]:not([disabled])');
            if (submitButton) {
                submitButton.click();
                await new Promise(r => setTimeout(r, 1000)); // 每次点击提交后等待1秒
            }
        }

        async _openApiPage() {
            // 等待一小段时间确保注册流程完全完成
            await new Promise(r => setTimeout(r, 2000));

            // 在新窗口中打开API页面
            const apiUrl = 'https://api.bujidian.com/getMailInfo?name=&pwd=';
            window.open(apiUrl, '_blank');

            this._safeLog('API page opened:', apiUrl);
        }

        _safeLog(...args) {
            if (this.debugMode && Math.random() > 0.7) {
                console.log('[DEBUG]', ...args);
            }
        }
    }

    // ================== 悬浮窗模块 ==================
    class FloatingWindow {
        constructor() {
            this.element = null;
            this.isDragging = false;
            this.currentX = 0;
            this.currentY = 0;
            this.initialX = 0;
            this.initialY = 0;
            this.xOffset = 0;
            this.yOffset = 0;
        }

        create() {
            // 创建悬浮窗容器
            this.element = document.createElement('div');
            this.element.id = 'auto-register-float';
            this.element.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                background: #4CAF50;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: move;
                user-select: none;
                box-shadow: 0 2px 5px rgba(0,0,0,0.2);
                transition: background-color 0.3s;
            `;

            // 创建按钮
            const button = document.createElement('button');
            button.textContent = '自动注册';
            button.style.cssText = `
                background: none;
                border: none;
                color: white;
                font-size: 14px;
                cursor: pointer;
                padding: 0;
                margin: 0;
                font-weight: bold;
            `;

            this.element.appendChild(button);
            document.body.appendChild(this.element);

            // 添加拖拽功能
            this._setupDragListeners();

            return button;
        }

        _setupDragListeners() {
            this.element.addEventListener('mousedown', (e) => {
                if (e.target === this.element) {
                    this.isDragging = true;
                    this.initialX = e.clientX - this.xOffset;
                    this.initialY = e.clientY - this.yOffset;
                    this.element.style.cursor = 'grabbing';
                }
            });

            document.addEventListener('mousemove', (e) => {
                if (this.isDragging) {
                    e.preventDefault();
                    this.currentX = e.clientX - this.initialX;
                    this.currentY = e.clientY - this.initialY;
                    this.xOffset = this.currentX;
                    this.yOffset = this.currentY;
                    this.element.style.transform = 
                        `translate(${this.currentX}px, ${this.currentY}px)`;
                }
            });

            document.addEventListener('mouseup', () => {
                if (this.isDragging) {
                    this.isDragging = false;
                    this.element.style.cursor = 'move';
                }
            });
        }
    }

    // ================== 主执行逻辑 ==================
    const isRegistrationPage = () =>
        /\/registration(?:\/.*|\?.*)?$/.test(window.location.href);

    const init = async () => {
        if (!isRegistrationPage()) return;

        // 防控制台检测
        window.console = new Proxy(console, {
            get: (target, prop) => ['log','error','warn'].includes(prop)
                ? (...args) => Math.random() > 0.3 && target[prop](...args)
                : target[prop]
        });

        // 创建悬浮窗
        const floatingWindow = new FloatingWindow();
        const triggerButton = floatingWindow.create();

        // 点击按钮时启动注册流程
        triggerButton.addEventListener('click', async () => {
            triggerButton.disabled = true;
            triggerButton.style.opacity = '0.7';
            triggerButton.textContent = '注册中...';
            
            try {
                await new RegistrationFlow().execute();
                triggerButton.textContent = '注册完成';
                triggerButton.style.opacity = '1';
            } catch (error) {
                triggerButton.textContent = '注册失败';
                triggerButton.style.opacity = '1';
                setTimeout(() => {
                    triggerButton.disabled = false;
                    triggerButton.textContent = '自动注册';
                }, 3000);
            }
        });
    };

    if (document.readyState === 'complete') {
        init();
    } else {
        window.addEventListener('load', init);
    }
})();
