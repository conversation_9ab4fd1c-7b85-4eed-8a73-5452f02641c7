# 邮箱验证链接提取器

这是一个专门用于在 `https://api.bujidian.com/getMailInfo` 页面自动提取验证链接的浏览器扩展。

## 功能特点

- 🔍 智能识别验证链接
- 🎯 自动提取并打开验证链接
- 🎨 可拖拽的悬浮窗界面
- 📱 实时状态反馈

## 安装方法

### 方法一：Chrome 扩展安装

1. 打开 Chrome 浏览器
2. 在地址栏输入 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `verification-extension` 文件夹
6. 扩展安装完成

### 方法二：用户脚本安装

1. 安装 Tampermonkey 或 Greasemonkey 扩展
2. 打开 `verification.js` 文件
3. 复制全部内容
4. 在 Tampermonkey 中创建新脚本
5. 粘贴代码并保存

## 使用方法

1. 访问 `https://api.bujidian.com/getMailInfo?name=邮箱&pwd=密码`
2. 页面右上角会出现蓝色的"提取验证链接"悬浮窗
3. 点击悬浮窗按钮
4. 脚本会自动分析页面内容并提取验证链接
5. 如果找到验证链接，会自动在新窗口打开

## 状态说明

- **蓝色** - "提取验证链接"：初始状态，可以点击
- **橙色** - "提取中..."：正在分析页面内容
- **绿色** - "已打开验证链接"：成功找到并打开验证链接
- **红色** - "未找到验证链接"：页面中没有找到验证链接

## 支持的验证链接格式

- 包含 `confirm`、`verify`、`activation`、`validate` 关键词的链接
- `lpts.me` 域名的链接
- 包含 `/a/` 路径的链接
- 包含 `token=` 或 `code=` 参数的链接

## 注意事项

- 确保浏览器允许弹出窗口
- 如果页面内容是动态加载的，可能需要等待内容完全加载后再点击按钮
- 扩展只在指定的 API 页面上运行，不会影响其他网站

## 故障排除

如果扩展不工作：

1. 检查是否在正确的页面（api.bujidian.com/getMailInfo）
2. 刷新页面重试
3. 检查浏览器控制台是否有错误信息
4. 确保扩展已正确安装并启用
